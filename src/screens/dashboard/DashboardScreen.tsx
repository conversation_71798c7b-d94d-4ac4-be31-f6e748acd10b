import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Image } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { RootState } from '../../store/store';
import { logout } from '../../store/slices/authSlice';
import { googleAuthService } from '../../services/googleAuth';
import { apiService, LedgerStatsResponse } from '../../services/apiService';
import { Transaction, TransactionType, TransactionStatus } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import { formatCurrency } from '../../utils/currency';
import { canCreateTransaction, canApproveTransactions } from '../../utils/permissions';
import { setCurrency } from '../../store/slices/currencySlice';
import { mapTransactionResponse } from '../../utils/mapping';
import NotificationBadge from '../../components/common/NotificationBadge';

export default function DashboardScreen() {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const { user, googleUser } = useSelector((state: RootState) => state.auth);
  const currency = useSelector((state: RootState) => state.currency.value);

  // State for ledger stats
  const [ledgerStats, setLedgerStats] = useState<LedgerStatsResponse | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);

  // State for transactions
  const [pendingTransactions, setPendingTransactions] = useState<Transaction[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [transactionsLoading, setTransactionsLoading] = useState(true);

  if (!user) return null;

  // Fetch ledger stats from API
  const fetchLedgerStats = async () => {
    try {
      setStatsLoading(true);
      const stats = await apiService.getLedgerStats();
      setLedgerStats(stats);
    } catch (error) {
      console.error('Error fetching ledger stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  // Fetch pending transactions for advisors
  const fetchPendingTransactions = async () => {
    if (!user || !canApproveTransactions(user.role, user.isAdvisor)) {
      return;
    }

    try {
      const apiTransactions = await apiService.getPendingTransactions();
      const convertedTransactions: Transaction[] = apiTransactions.map(mapTransactionResponse);
      const sortedTransactions = convertedTransactions.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
      setPendingTransactions(sortedTransactions);
    } catch (error) {
      console.error('Error fetching pending transactions:', error);
      setPendingTransactions([]);
    }
  };

  // Fetch transaction history for all users
  const fetchTransactions = async () => {
    try {
      setTransactionsLoading(true);
      const apiTransactions = await apiService.getTransactionHistory();
      setTransactions(apiTransactions);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setTransactions([]);
    } finally {
      setTransactionsLoading(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      fetchLedgerStats();
      fetchTransactions();
      fetchPendingTransactions();
    }, [])
  );

  // Fetch default currency from API
  useEffect(() => {
    const fetchCurrency = async () => {
      try {
        const res = await apiService.getDefaultCurrency();
        if (res && res.value) {
          dispatch(setCurrency(res.value));
        }
      } catch (e) {
        // fallback to default USD
      }
    };
    fetchCurrency();
  }, [dispatch]);

  // Calculate statistics from API data
  const totalReceived = ledgerStats ? parseFloat(ledgerStats.total_received) : 0;
  const totalSpent = ledgerStats ? parseFloat(ledgerStats.total_spent) : 0;
  const currentBalance = totalReceived - totalSpent;

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          onPress: async () => {
            try {
              await googleAuthService.signOut();
              dispatch(logout());
            } catch (error) {
              console.error('Logout error:', error);
              dispatch(logout()); // Logout anyway
            }
          }
        },
      ]
    );
  };

  const navigateToCreateTransaction = (type: TransactionType) => {
    navigation.navigate('CreateTransaction', { type });
  };

  const navigateToApprovals = () => {
    navigation.navigate('ApprovalList', { pendingTransactions });
  };

  const navigateToTransactions = () => {
    navigation.navigate('TransactionList', { transactions });
  };

  // Calculate rejected transactions count for students
  const rejectedTransactionsCount = transactions.filter(
    t => t.status === TransactionStatus.REJECTED
  ).length;

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.userInfo}>
            {googleUser?.photo && (
              <Image
                source={{ uri: googleUser.photo }}
                style={styles.profileImage}
              />
            )}
            <View style={styles.userText}>
              <Text style={styles.greeting}>Hello, {(user.name ? user.name.split(' ')[0] : user.first_name || 'User')}!</Text>
              <Text style={styles.role}>
                {user.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : ''}
                {user.isAdvisor && ' • Advisor'}
              </Text>
              {googleUser?.email && (
                <Text style={styles.email}>{googleUser.email}</Text>
              )}
            </View>
          </View>
          <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>

        {/* Statistics Cards */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your spending</Text>
          <View style={styles.statsContainer}>

            <View style={styles.statCard}>
              <Text style={styles.statValue}>
                {statsLoading ? '...' : formatCurrency(totalReceived, currency)}
              </Text>
              <Text style={styles.statLabel}>Cash Given</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statValue, { color: Colors.error }]}>
                {statsLoading ? '...' : formatCurrency(totalSpent, currency)}
              </Text>
              <Text style={styles.statLabel}>Cash Spent</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statValue, { color: Colors.textPrimary }]}>
                {statsLoading ? '...' : formatCurrency(currentBalance, currency)}
              </Text>
              <Text style={styles.statLabel}>Current Balance</Text>
            </View>
          </View>

        </View>


        {/* View My Transactions */}
        <View style={styles.sectionHeader}>
          <TouchableOpacity onPress={navigateToTransactions}>
            <View style={styles.transactionHeaderContainer}>
              <Text style={styles.seeAllText}>View My Transactions</Text>
              {rejectedTransactionsCount > 0 && (
                <View style={styles.rejectedTransactionInfo}>
                  <NotificationBadge
                    count={rejectedTransactionsCount}
                    size="small"
                    backgroundColor={Colors.error}
                  />
                  <Text style={styles.rejectedTransactionText}>
                    Rejected Transactions
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionGrid}>
            {canCreateTransaction(user.role, TransactionType.SPEND) && (
              <TouchableOpacity
                style={styles.actionCard}
                onPress={() => navigateToCreateTransaction(TransactionType.SPEND)}
              >
                <Text style={styles.actionIcon}>💳</Text>
                <Text style={styles.actionTitle}>Log Cash Spent</Text>
                <Text style={styles.actionDescription}>Record expenses and purchases</Text>
              </TouchableOpacity>
            )}

            {canCreateTransaction(user.role, TransactionType.TRANSFER) && (
              <TouchableOpacity
                style={styles.actionCard}
                onPress={() => navigateToCreateTransaction(TransactionType.TRANSFER)}
              >
                <Text style={styles.actionIcon}>💰</Text>
                <Text style={styles.actionTitle}>Log Cash Given</Text>
                <Text style={styles.actionDescription}>Give cash to student or staff</Text>
              </TouchableOpacity>
            )}

            {canCreateTransaction(user.role, TransactionType.RETURNED) && (
              <TouchableOpacity
                style={styles.actionCard}
                onPress={() => navigateToCreateTransaction(TransactionType.RETURNED)}
              >
                <Text style={styles.actionIcon}>↩️</Text>
                <Text style={styles.actionTitle}>Log Cash Returned</Text>
                <Text style={styles.actionDescription}>Return unused cash to school</Text>
              </TouchableOpacity>
            )}

            {canApproveTransactions(user.role, user.isAdvisor) && (
              <TouchableOpacity
                style={styles.actionCard}
                onPress={navigateToApprovals}
              >
                <View style={styles.cardHeader}>
                  <Text style={styles.actionIcon}>✅</Text>
                  <NotificationBadge count={pendingTransactions.length} size="small" />
                </View>
                <Text style={styles.actionTitle}>Pending Approval</Text>
                <Text style={styles.actionDescription}>
                  {pendingTransactions.length > 0
                    ? `${pendingTransactions.length} transaction${pendingTransactions.length > 1 ? 's' : ''} waiting approval`
                    : 'Approve/reject student transactions'
                  }
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Footer - Always at bottom */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Need Help?  Contact your Advisor or Tech Support
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  scrollContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 80,
    backgroundColor: Colors.background,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  userText: {
    flex: 1,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  role: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  email: {
    fontSize: 12,
    color: Colors.textTertiary,
    marginTop: 2,
  },
  logoutButton: {
    padding: 8,
  },
  logoutText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 12,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingVertical: 30,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.success,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginTop: 4,
  },
  section: {
    marginTop: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    padding: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  seeAllText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 12,
    gap: 12,
  },
  actionCard: {
    width: '47%',
    backgroundColor: Colors.background,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  actionIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.textTertiary,
    marginTop: 8,
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: Colors.error + '20',
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.error + '40',
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    textAlign: 'center',
  },
  footer: {
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: 30,
    borderTopWidth: 1,
    borderTopColor: Colors.surface,
  },
  footerText: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  transactionHeaderContainer: {
    alignItems: 'center',
  },
  rejectedTransactionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 6,
  },
  rejectedTransactionText: {
    fontSize: 12,
    color: Colors.error,
    fontWeight: '500',
  },
});
